# -*- coding: utf-8 -*-
"""
UniSolver - Orchestrator

This is the main orchestrator that implements the UniSolver workflow using
a modular agent architecture. It coordinates the execution of six specialized
agents to solve optimization problems.

Based on the UniSolver paper architecture.
"""

import os
import json
import time
import traceback
import argparse
from rich.markdown import Markdown
from rich.console import Console
from rich.progress import Progress, BarColumn, TextColumn, TimeElapsedColumn, ProgressColumn

# Import agents
from typing import List
from agents import (
    InformationAgent,
    ModelingAgent,
    CodeAgent,
    RepairAgent,
    VerifierAgent,
    ModelDefectReport
)

# Import utilities
from utils import (
    execute_in_sandbox,
    print_header,
    eval_model_result,
    is_number_string
)

# Import configuration
from config import get_config


def run_unisolver_workflow(problem: str, model_name: str = "openai/o3-mini", max_trials: int = 3) -> tuple:
    """
    Execute the UniSolver 2.0 workflow using the enhanced agent architecture.
    
    Enhanced with:
    - Simplified mathematical modeling
    - Hierarchical knowledge construction
    - Dual-layer repair system
    - Self-critique formulation
    
    Args:
        problem (str): The optimization problem description
        model_name (str): LLM model name to use
        max_trials (int): Maximum number of repair attempts
        
    Returns:
        tuple: (success: bool, final_response: str)
    """
    print_header("UniSolver 2.0 Workflow Started")
    
    # Load configuration
    config = get_config()
    
    # Phase 1: Initialization - Create enhanced agent instances
    print_header("Initialization Phase - Creating Enhanced Agent Instances")
    info_agent = InformationAgent(model_name)
    model_agent = ModelingAgent(model_name)
    code_agent = CodeAgent(model_name)
    repair_agent = RepairAgent(model_name)
    
    # Reset repair state for new problem
    repair_agent.reset_repair_state()
    
    # Phase 2: Hierarchical Knowledge Construction
    print_header("Hierarchical Knowledge Construction Phase")
    knowledge_matrix = info_agent.run(problem)
    
    # Phase 3: Mathematical Modeling
    print_header("Mathematical Modeling Phase")
    math_model = model_agent.run(problem, knowledge_matrix)
    
    # Phase 4: Enhanced Code Generation
    print_header("Enhanced Code Generation Phase")
    code = code_agent.run(math_model)
    
    # Phase 5: Dual-Layer Execution and Repair Loop
    print_header("Dual-layer Execution and Repair Phase")
    solution = None
    model_reconstruction_count = 0
    max_model_reconstructions = 2
    
    # Main dual-layer repair loop
    while model_reconstruction_count <= max_model_reconstructions:
        print(f"\n=== Model Iteration {model_reconstruction_count + 1} ===")
        
        # Code-level repair sub-loop
        code_repair_success = False
        
        for trial in range(max_trials):
            print(f"\n--- Code Execution Attempt {trial + 1}/{max_trials} ---")
            
            # Execute code in sandbox
            print("[Orchestrator] Executing code in sandbox...")
            result, error = execute_in_sandbox(code)
            
            if error is None:  # Success
                print("[Orchestrator] Code execution successful!")
                solution = result
                code_repair_success = True
                break
            else:  # Error occurred
                print(f"[Orchestrator] Code execution failed: {error}")
                
                if trial < max_trials - 1:  # Not the last trial
                    print("[Orchestrator] Requesting dual-layer repair...")
                    
                    # Attempt dual-layer repair
                    repaired_code, defect_report = repair_agent.run(
                        code, error, knowledge_matrix, problem
                    )
                    
                    if repaired_code is not None:
                        # Code-level repair succeeded
                        print("[Orchestrator] Code-level repair successful")
                        code = repaired_code
                        continue
                    elif defect_report is not None:
                        # Model-level issues detected
                        print(f"[Orchestrator] Model-level defect detected: {defect_report.defect_type}")
                        
                        if defect_report.requires_model_reconstruction:
                            print("[Orchestrator] Triggering model reconstruction...")
                            
                            # Enhanced knowledge matrix with defect feedback
                            enhanced_knowledge = knowledge_matrix.copy()
                            enhanced_knowledge['defect_feedback'] = {
                                'defect_type': defect_report.defect_type,
                                'description': defect_report.description,
                                'suggested_fixes': defect_report.suggested_fixes,
                                'failed_model': math_model,
                                'failed_code': code,
                                'error_message': error
                            }
                            
                            # Reconstruct mathematical model
                            print("[Orchestrator] Reconstructing mathematical model...")
                            math_model = model_agent.run(problem, enhanced_knowledge)
                            
                            # Regenerate code
                            print("[Orchestrator] Regenerating code...")
                            code = code_agent.run(math_model)
                            
                            # Reset repair state for new model
                            repair_agent.reset_repair_state()
                            model_reconstruction_count += 1
                            break
                        else:
                            print("[Orchestrator] Unable to repair code or model")
                            break
                    else:
                        print("[Orchestrator] Repair system failed")
                        break
                else:
                    print("[Orchestrator] Maximum code repair attempts reached")
                    break
        
        if code_repair_success:
            break
        elif model_reconstruction_count > max_model_reconstructions:
            print("[Orchestrator] Maximum model reconstruction attempts reached, execution failed")
            break
    
    # Phase 6: Final Verification and Answer Generation
    print_header("Final Verification and Answer Generation Phase")
    if solution is None:
        print("[Orchestrator] Unable to obtain valid solution, returning failure information")
        return False, (
            "After dual-layer repair attempts, still unable to obtain a valid solution.\n"
            "Possible reasons include: incomplete problem description, conflicting constraints, or model complexity exceeding current processing capabilities.\n"
            "Suggestion: Re-examine the problem description to ensure all constraints are clear and consistent."
        )
    
    verifier = VerifierAgent(model_name)
    final_response = verifier.run(problem, knowledge_matrix, solution)
    
    # Performance summary
    repair_summary = repair_agent.get_repair_summary()
    print(f"\n[Orchestrator] Performance Summary:")
    print(f"   • Code repair attempts: {repair_summary['total_attempts']}")
    print(f"   • Model reconstruction count: {model_reconstruction_count}")
    
    print_header("UniSolver 2.0 Workflow Completed")
    return True, final_response


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="UniSolver - Agent-based Operations Research Optimization Problem Solver",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Example usage:
  python run.py --problem "A factory produces two products A and B, find the production plan for maximum profit"
  python run.py --problem "Transportation problem: transport goods from 3 supply points to 4 demand points, find minimum transportation cost" --max_trials 5
        """
    )
    
    parser.add_argument(
        '--problem', 
        help='Optimization problem description (if not provided, batch processing mode will run)'
    )
    
    parser.add_argument(
        '--model', 
        default='openai/o3-mini', 
        help='LLM model name (default: openai/o3-mini)'
    )
    
    parser.add_argument(
        '--max_trials', 
        type=int, 
        default=3, 
        help='Maximum repair attempts (default: 3)'
    )
    
    # For batch processing (optional, for compatibility)
    parser.add_argument(
        '--data_path', 
        default='data/complexor_combined_result.json',
        help='Dataset file path (default: data/complexor_combined_result.json)'
    )
    
    return parser.parse_args()


def main():
    """Main function to run UniSolver 2.0."""
    import json
    args = parse_args()
    
    # Check if running in batch mode or single problem mode
    if not args.problem and args.data_path and os.path.exists(args.data_path):
        # Batch processing mode (for compatibility with existing datasets)
        print_header("UniSolver 2.0 Batch Processing Mode")
        
        with open(args.data_path, 'r', encoding='utf-8') as f:
            dataset = json.load(f)
        
        console = Console()
        pass_count = 0
        correct_count = 0
        error_datas = []
        results = {}
        total_repair_attempts = 0
        total_model_reconstructions = 0
        
        # Create results directory
        os.makedirs('results', exist_ok=True)
        
        # Initialize progress bar with custom accuracy tracking
        class AccuracyColumn(ProgressColumn):
            def render(self, task):
                current_processed = int(task.completed)
                accuracy_rate = (correct_count / current_processed * 100) if current_processed > 0 else 0
                return f"[bold cyan]Accuracy: {accuracy_rate:.1f}%"
        
        with Progress(
            TextColumn("[bold blue]UniSolver 2.0 Processing Progress"),
            BarColumn(bar_width=40),
            "[progress.percentage]{task.percentage:>3.1f}%",
            "•",
            TextColumn("[bold green]Passed: {task.completed}/{task.total}"),
            "•", 
            AccuracyColumn(),
            "•",
            TimeElapsedColumn(),
            console=console
        ) as progress:
            task = progress.add_task(
                 "Processing...", 
                 total=len(dataset)
             )
            
            for i, d in dataset.items():
                print_header(f"UniSolver 2.0 Processing Problem {i}")
                user_question, answer = d['question'], d['answer']
                
                # Display question using rich markdown
                md = Markdown(user_question)
                console.print(md)
                print('-------------')
                
                # Run UniSolver 2.0 workflow
                is_solve_success, final_response = run_unisolver_workflow(
                    user_question, args.model, args.max_trials
                )
                
                # Extract numerical result for evaluation
                llm_result = None
                if is_solve_success:
                    # Try to extract numerical value from JSON response first
                    import re
                    import json
                    
                    # First try to parse as JSON and extract objective_value
                    try:
                        # Clean the response to extract JSON
                        json_match = re.search(r'\{[\s\S]*\}', final_response)
                        if json_match:
                            json_str = json_match.group(0)
                            parsed_json = json.loads(json_str)
                            
                            # Extract objective_value from machine_readable_data
                            if 'machine_readable_data' in parsed_json:
                                obj_val = parsed_json['machine_readable_data'].get('objective_value')
                                if obj_val is not None and obj_val != 'null':
                                    llm_result = float(obj_val)
                    except (json.JSONDecodeError, ValueError, KeyError):
                        pass
                    
                    # If JSON extraction failed, use pattern matching
                    if llm_result is None:
                        # Define patterns to match different response formats
                        value_patterns = [
                            r'"objective_value"[:\s]*([\d.e+-]+)',
                            r'Optimal solution value[:\s]*([\d.e+-]+)',
                            r'Best objective[:\s]*([\d.e+-]+)', 
                            r'Optimal objective[:\s]*([\d.e+-]+)',
                            r'Final result[:\s]*([\d.e+-]+)',
                            r'Solution[:\s]*([\d.e+-]+)',
                            r'Answer[:\s]*([\d.e+-]+)',
                            r'Value[:\s]*([\d.e+-]+)',
                            r'Result[:\s]*([\d.e+-]+)',
                            r'Total cost[:\s]*([\d.e+-]+)',
                            r'Minimum cost[:\s]*([\d.e+-]+)',
                            r'Maximum profit[:\s]*([\d.e+-]+)',
                            r'Optimal value[:\s]*([\d.e+-]+)'
                        ]
                        
                        # Try each pattern
                        for pattern in value_patterns:
                            matches = re.findall(pattern, final_response, re.IGNORECASE)
                            if matches:
                                try:
                                    llm_result = float(matches[0])
                                    break
                                except ValueError:
                                    continue
                        
                        # If no specific pattern matches, try to find meaningful numbers
                        if llm_result is None:
                            # Look for numbers that are likely to be objective values
                            numbers = re.findall(r'[-+]?\d*\.?\d+(?:[eE][-+]?\d+)?', final_response)
                            if numbers:
                                # Convert to floats and filter reasonable values
                                valid_numbers = []
                                for num_str in numbers:
                                    try:
                                        num = float(num_str)
                                        # Filter out very small numbers that are likely not objective values
                                        if abs(num) >= 0.01:  
                                            valid_numbers.append(num)
                                    except ValueError:
                                        continue
                                
                                # Take the largest meaningful number as it's most likely the objective
                                if valid_numbers:
                                    llm_result = max(valid_numbers, key=abs)
                
                # Evaluate results
                try:
                    pass_flag, correct_flag = eval_model_result(is_solve_success, llm_result, answer)
                    pass_count += 1 if pass_flag else 0
                    correct_count += 1 if correct_flag else 0
                    
                    if not pass_flag or not correct_flag:
                        error_datas.append(i)
                    
                    results[i] = {
                        'question': d['question'],
                        'answer': d['answer'],
                        'model_result': llm_result,
                        'final_response': final_response,
                        'pass': pass_flag,
                        'correct': correct_flag,
                        'framework_version': '2.0'
                    }
                    
                    print(f'solve: {is_solve_success}, llm: {llm_result}, ground truth: {answer}')
                    print(f'[Final] run pass: {pass_flag}, solve correct: {correct_flag}')
                    print(' ')
                    
                    # Update progress bar
                    progress.update(task, advance=1)
                    
                    # Print current statistics with accuracy rate
                    current_processed = int(progress.tasks[0].completed)
                    accuracy_rate = (correct_count / current_processed * 100) if current_processed > 0 else 0
                    console.print(f"\r[green]Passed: {pass_count}/{current_processed} | [cyan]Accuracy: {accuracy_rate:.1f}% ({correct_count}/{current_processed})", end="")
                    
                except Exception as e:
                    print(f"Error processing item {i}: {e}")
                    error_datas.append(i)
                    results[i] = {
                        'question': d['question'],
                        'answer': d['answer'],
                        'model_result': None,
                        'final_response': final_response,
                        'pass': False,
                        'correct': False,
                        'error': str(e),
                        'framework_version': '2.0'
                     }
                    
                    # Update progress bar even for errors
                    progress.update(task, advance=1)
                    
                    # Print current statistics with accuracy rate
                    current_processed = int(progress.tasks[0].completed)
                    accuracy_rate = (correct_count / current_processed * 100) if current_processed > 0 else 0
                    console.print(f"\r[green]Passed: {pass_count}/{current_processed} | [cyan]Accuracy: {accuracy_rate:.1f}% ({correct_count}/{current_processed}) | [red]Errors: {len(error_datas)}", end="")
        
            # Auto-save results
        print(f'\n[Total {len(dataset)}] run pass: {pass_count}, solve correct: {correct_count}')
        print(f'[Total fails {len(error_datas)}] error datas: {error_datas}')
        
        # Generate timestamp filename
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        filename = f"results/UniSolver_2_0_agent_results_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump({
                'framework_version': '2.0',
                'timestamp': timestamp,
                'pass_count': pass_count,
                'correct_count': correct_count,
                'total': len(dataset),
                'pass_rate': round(pass_count / len(dataset) * 100, 2),
                'correct_rate': round(correct_count / len(dataset) * 100, 2),
                'error_datas': error_datas,
                'details': results,
                'enhancements': [
                    'Simplified mathematical modeling',
                    'Hierarchical knowledge construction',
                    'Dual-layer repair system',
                    'Self-critique response generation'
                ]
            }, f, indent=2, ensure_ascii=False)
        
        print(f'\n✅ UniSolver 2.0 batch processing completed!')
        print(f'📊 Final Statistics:')
        print(f'   - Total problems: {len(dataset)}')
        print(f'   - Passed count: {pass_count} ({pass_count/len(dataset)*100:.1f}%)')
        print(f'   - Correct count: {correct_count} ({correct_count/len(dataset)*100:.1f}%)')
        print(f'   - Failed count: {len(error_datas)}')
        print(f'📁 Results saved to: {filename}')
        
    elif args.problem:
        # Single problem mode
        print_header("UniSolver 2.0 Single Problem Solving Mode")
        
        # Run UniSolver 2.0 workflow
        success, final_response = run_unisolver_workflow(
            args.problem, args.model, args.max_trials
        )
        
        # Display results
        print_header("Final Results")
        if success:
            print("✅ Solving successful!")
            print("\n📋 Detailed Answer:")
            print("-" * 50)
            print(final_response)
            print("-" * 50)
        else:
            print("❌ Solving failed!")
            print(f"\nError message: {final_response}")
    
    else:
        # No problem specified and no valid data path, show help
        print("❌ Error: Please provide a problem description or ensure the default dataset file exists")
        print("\nUsage:")
        print("  1. Single problem mode: python run.py --problem \"Your optimization problem description\"")
        print("  2. Batch processing mode: python run.py (using default dataset)")
        print("  3. Custom dataset: python run.py --data_path your_dataset.json")
        print("\nUniSolver 2.0 New Features:")
        print("  • Simplified mathematical modeling")
        print("  • Hierarchical knowledge construction")
        print("  • Dual-layer repair system")
        print("  • Self-critical response generation")


if __name__ == "__main__":
    main()