# -*- coding: utf-8 -*-
"""
Formulator Agent with Self-Critique

This agent is responsible for converting numerical solutions into natural language responses
with integrated verification capabilities. It combines the original FormulatorAgent with
VerifierAgent functionality to provide self-critique and validation.

Upgraded from simple response generation to intelligent self-validating formulator.
"""

from utils import query_llm


class FormulatorAgent:
    """Agent responsible for formulating and self-validating natural language responses."""
    
    def __init__(self, model_name="openai/o3-mini"):
        """
        Initialize the Formulator Agent with self-critique capabilities.
        
        Args:
            model_name (str): LLM model name to use for processing
        """
        self.model_name = model_name
    
    def _create_focused_prompt(self, knowledge_matrix: dict, problem: str) -> str:
        """Create a focused prompt with key information at start and end."""
        if not knowledge_matrix:
            return "", ""
            
        core_info = f"""Core Problem: {problem}
Core Knowledge: {knowledge_matrix.get('core_knowledge', '')[:300]}..."""
        
        full_context = f"""## Hierarchical Knowledge Structure:
{knowledge_matrix.get('hierarchical_summary', '')}

## Detailed Context:
{knowledge_matrix.get('focused_context', '')}"""
        
        return core_info, full_context
    
    def _generate_initial_response(self, problem: str, knowledge_matrix: dict, 
                                  solution: dict, model: str = "") -> str:
        """Generate initial natural language response."""
        print("[FormulatorAgent-Enhanced] Generating initial answer...")
        
        core_info, full_context = self._create_focused_prompt(knowledge_matrix, problem)
        
        messages = [
            {"role": "system", "content": (
                "You are an operations research expert and technical writing expert. Convert numerical solution results into clear, accurate natural language answers.\n"
                f"{core_info}\n"
                "Requirements:\n"
                "1. Directly solve the user's original problem\n"
                "2. Include specific numerical results and explanations\n"
                "3. Combine domain knowledge for interpretation\n"
                "4. Use professional but understandable language\n"
                "5. Clear structure and complete logic"
            )},
            {"role": "user", "content": full_context if full_context else "No additional context"},
            {"role": "user", "content": f"Original problem:\n{problem}"},
            {"role": "user", "content": f"Mathematical model:\n{model}"},
            {"role": "user", "content": f"Solution results:\nStatus: {solution.get('status', 'unknown')}\nOptimal value: {solution.get('result', 'N/A')}\nExecution output: {solution.get('output', 'N/A')}"},
            {"role": "user", "content": (
                "Please generate a complete and accurate natural language answer including:\n"
                "1. Problem solution\n"
                "2. Key numerical results\n"
                "3. Practical significance of results\n"
                "4. Implementation recommendations (if applicable)\n"
                f"Important reminder: {core_info}"
            )}
        ]
        
        response = query_llm(messages, self.model_name)
        return response
    
    def _perform_self_critique(self, problem: str, model: str, solution: dict, 
                              initial_response: str, knowledge_matrix: dict) -> dict:
        """Perform mandatory self-critique on the generated response."""
        print("[FormulatorAgent-Enhanced] Performing mandatory self-critique...")
        
        core_info, full_context = self._create_focused_prompt(knowledge_matrix, problem)
        
        messages = [
            {"role": "system", "content": (
                "You are a quality assurance expert. Perform strict self-critique on the generated answer.\n"
                f"{core_info}\n"
                "Critique dimensions:\n"
                "1. Faithfulness: Is the answer completely faithful to the mathematical model and solution results?\n"
                "2. Completeness: Does it directly solve the user's original problem?\n"
                "3. Clarity: Are there any unclear expressions?\n"
                "4. Accuracy: Are numerical results correctly referenced?\n"
                "5. Logic: Is the reasoning process reasonable?"
            )},
            {"role": "user", "content": full_context if full_context else "No additional context"},
            {"role": "user", "content": f"Original problem:\n{problem}"},
            {"role": "user", "content": f"Mathematical model:\n{model}"},
            {"role": "user", "content": f"Solution results:\nStatus: {solution.get('status', 'unknown')}\nOptimal value: {solution.get('result', 'N/A')}\nExecution output: {solution.get('output', 'N/A')}"},
            {"role": "user", "content": f"Generated answer:\n{initial_response}"},
            {"role": "user", "content": (
                "Please perform strict self-critique and output in JSON format:\n"
                "{\"faithfulness\": {\"score\": 1-10, \"issues\": [\"issue list\"]}, \"completeness\": {\"score\": 1-10, \"issues\": [\"issue list\"]}, \"clarity\": {\"score\": 1-10, \"issues\": [\"issue list\"]}, \"accuracy\": {\"score\": 1-10, \"issues\": [\"issue list\"]}, \"logic\": {\"score\": 1-10, \"issues\": [\"issue list\"]}, \"overall_pass\": true/false, \"improvement_needed\": [\"improvement suggestion list\"]}\n"
                f"Important reminder: {core_info}"
            )}
        ]
        
        response = query_llm(messages, self.model_name)
        
        try:
            import json
            import re
            # Extract JSON from response
            json_match = re.search(r'\{[^{}]*\}', response, re.DOTALL)
            if json_match:
                critique_data = json.loads(json_match.group())
            else:
                # Fallback critique
                critique_data = {
                    "faithfulness": {"score": 7, "issues": []},
                    "completeness": {"score": 7, "issues": []},
                    "clarity": {"score": 7, "issues": []},
                    "accuracy": {"score": 7, "issues": []},
                    "logic": {"score": 7, "issues": []},
                    "overall_pass": True,
                    "improvement_needed": []
                }
        except Exception as e:
            print(f"[FormulatorAgent-Enhanced] Failed to parse critique results: {e}")
            critique_data = {
                "faithfulness": {"score": 6, "issues": ["Parsing failed"]},
                    "completeness": {"score": 6, "issues": ["Parsing failed"]},
                    "clarity": {"score": 6, "issues": ["Parsing failed"]},
                    "accuracy": {"score": 6, "issues": ["Parsing failed"]},
                    "logic": {"score": 6, "issues": ["Parsing failed"]},
                    "overall_pass": False,
                    "improvement_needed": ["Regenerate answer"]
            }
        
        return critique_data
    
    def _improve_response(self, problem: str, model: str, solution: dict, 
                         initial_response: str, critique: dict, knowledge_matrix: dict) -> str:
        """Improve the response based on self-critique feedback."""
        print("[FormulatorAgent-Enhanced] Improving answer based on self-critique...")
        
        core_info, full_context = self._create_focused_prompt(knowledge_matrix, problem)
        
        # Extract improvement suggestions
        improvement_points = []
        for dimension in ['faithfulness', 'completeness', 'clarity', 'accuracy', 'logic']:
            if dimension in critique and critique[dimension].get('score', 10) < 8:
                issues = critique[dimension].get('issues', [])
                improvement_points.extend(issues)
        
        improvement_points.extend(critique.get('improvement_needed', []))
        
        messages = [
            {"role": "system", "content": (
                "You are an answer improvement expert. Based on self-critique feedback, improve the generated answer.\n"
                f"{core_info}\n"
                "Improvement principles:\n"
                "1. Address all identified issues\n"
                "2. Maintain answer completeness and accuracy\n"
                "3. Ensure clear logic and accurate expression\n"
                "4. Directly solve user problems"
            )},
            {"role": "user", "content": full_context if full_context else "No additional context"},
            {"role": "user", "content": f"Original problem:\n{problem}"},
            {"role": "user", "content": f"Mathematical model:\n{model}"},
            {"role": "user", "content": f"Solution results:\nStatus: {solution.get('status', 'unknown')}\nOptimal value: {solution.get('result', 'N/A')}\nExecution output: {solution.get('output', 'N/A')}"},
            {"role": "user", "content": f"Initial answer:\n{initial_response}"},
            {"role": "user", "content": f"Issues to improve:\n" + "\n".join(improvement_points) if improvement_points else "No specific issues"},
            {"role": "user", "content": (
                "Please generate an improved complete answer that addresses all identified issues.\n"
                f"Important reminder: {core_info}"
            )}
        ]
        
        improved_response = query_llm(messages, self.model_name)
        return improved_response
    
    def run(self, solution: dict, problem: str, knowledge_matrix: dict, initial_answer: str = None, verification_summary: str = None) -> str:
        """
        Formulate a natural language response with mandatory self-critique and improvement,
        incorporating verification results if provided.
        
        Args:
            solution (dict): Numerical solution from sandbox execution
            problem (str): Original optimization problem description
            knowledge_matrix (dict): Knowledge matrix for context
            initial_answer (str, optional): Pre-generated initial answer from verifier
            verification_summary (str, optional): Summary of verification results
        
        Returns:
            str: Validated and improved natural language response including verification info
        """
        print("[FormulatorAgent-Enhanced] Starting enhanced answer generation process...")
        
        if initial_answer:
            print("[FormulatorAgent-Enhanced] Using provided initial answer from verification")
            response = initial_answer
        else:
            response = self._generate_initial_response(problem, knowledge_matrix, solution)
        
        # Incorporate verification summary if available
        if verification_summary:
            response += f"\n\n**Verification Summary:**\n{verification_summary}"
        
        # Proceed with self-critique and improvement
        critique = self._perform_self_critique(problem, "", solution, response, knowledge_matrix)
        
        overall_pass = critique.get('overall_pass', False)
        min_score_threshold = 7
        needs_improvement = False
        for dimension in ['faithfulness', 'completeness', 'clarity', 'accuracy', 'logic']:
            if dimension in critique and critique[dimension].get('score', 10) < min_score_threshold:
                needs_improvement = True
                break
        
        if not overall_pass or needs_improvement:
            print("[FormulatorAgent-Enhanced] Self-critique failed, performing improvement...")
            final_response = self._improve_response(problem, "", solution, response, critique, knowledge_matrix)
            
            print("[FormulatorAgent-Enhanced] Performing second validation on improved answer...")
            second_critique = self._perform_self_critique(problem, "", solution, final_response, knowledge_matrix)
            
            if second_critique.get('overall_pass', True):
                print("[FormulatorAgent-Enhanced] Second validation passed")
            else:
                print("[FormulatorAgent-Enhanced] Second validation still has issues, but using improved answer")
        else:
            print("[FormulatorAgent-Enhanced] Self-critique passed, using initial answer")
            final_response = response
        
        print("[FormulatorAgent-Enhanced] Enhanced answer generation completed")
        return final_response
    
    def get_critique_summary(self, problem: str, model: str, solution: dict, 
                           response: str, knowledge_matrix: dict) -> dict:
        """Get a summary of the self-critique for debugging purposes."""
        return self._perform_self_critique(problem, model, solution, response, knowledge_matrix)