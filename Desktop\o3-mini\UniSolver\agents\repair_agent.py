# -*- coding: utf-8 -*-
"""
Repair Agent - Dual-Layer Repair System

This agent implements a two-layer repair mechanism:
1. Code-level repair: Direct code fixes for syntax/logic errors
2. Model-level feedback: Identifies fundamental modeling issues and provides
   feedback to ModelingAgent for model reconstruction

Upgraded from simple code repair to intelligent dual-layer system.
"""

from typing import Tuple, Optional, Dict
from utils import query_llm


class ModelDefectReport:
    """Represents a model defect feedback report."""
    
    def __init__(self, defect_type: str, description: str, 
                 suggested_fixes: list, severity: str = "medium"):
        self.defect_type = defect_type  # e.g., "missing_constraint", "variable_type_error"
        self.description = description
        self.suggested_fixes = suggested_fixes
        self.severity = severity  # "low", "medium", "high", "critical"
        self.requires_model_reconstruction = severity in ["high", "critical"]


class RepairAgent:
    """Agent responsible for dual-layer code and model repair."""
    
    def __init__(self, model_name="openai/o3-mini"):
        """
        Initialize the Repair Agent with dual-layer capabilities.
        
        Args:
            model_name (str): LLM model name to use for processing
        """
        self.model_name = model_name
        self.repair_attempts = 0
        self.max_code_repairs = 3  # Maximum code-level repair attempts
        self.repair_history = []   # Track repair attempts
    
    def _create_focused_prompt(self, knowledge_matrix: dict, problem: str) -> str:
        """Create a focused prompt with key information at start and end."""
        if not knowledge_matrix:
            return "", ""
            
        core_info = f"""Core Problem: {problem}
Core Knowledge: {knowledge_matrix.get('core_knowledge', '')[:300]}..."""
        
        full_context = f"""## Hierarchical Knowledge Structure:
{knowledge_matrix.get('hierarchical_summary', '')}

## Detailed Context:
{knowledge_matrix.get('focused_context', '')}"""        
        return core_info, full_context
    
    def _analyze_error_type(self, error_message: str, failed_code: str) -> str:
        """Analyze the error to determine if it's code-level or model-level."""
        print("[RepairAgent-Dual] Analyzing error type...")
        
        messages = [
            {"role": "system", "content": (
                "You are an error analysis expert. Analyze error types and classify as:\n"
                "1. CODE_LEVEL: Syntax errors, variable name errors, API usage errors, import errors, encoding issues\n"
                "2. MODEL_LEVEL: Mathematical model defects, incomplete constraints, variable type errors, logic errors\n"
                "3. MIXED: Contains both code and model level issues\n"
                "Always respond in English only."
            )},
            {"role": "user", "content": f"Error message:\n{error_message}"},
            {"role": "user", "content": f"Failed code:\n{failed_code[:500]}..."},
            {"role": "user", "content": (
                "Please analyze the error type and only respond with: CODE_LEVEL, MODEL_LEVEL, or MIXED"
            )}
        ]
        
        response = query_llm(messages, self.model_name)
        error_type = response.strip().upper()
        
        if error_type not in ["CODE_LEVEL", "MODEL_LEVEL", "MIXED"]:
            error_type = "CODE_LEVEL"  # Default to code level
        
        print(f"[RepairAgent-Dual] Error type: {error_type}")
        return error_type
    
    def _perform_code_level_repair(self, failed_code: str, error_message: str, 
                                  knowledge_matrix: dict = None, problem: str = "") -> str:
        """Perform code-level repair (Layer 1)."""
        print(f"[RepairAgent-Dual] Executing code-level repair (attempt {self.repair_attempts + 1}/{self.max_code_repairs})...")
        
        core_info, full_context = self._create_focused_prompt(knowledge_matrix, problem)
        
        messages = [
            {"role": "system", "content": (
                "You are a Gurobi optimization programming expert. Focus on fixing code-level errors.\n"
                f"{core_info}\n"
                "STRICT REQUIREMENTS:\n"
                "1. Fix ONLY syntax, API usage, variable name, import, and encoding issues\n"
                "2. Do NOT change the core logic of the mathematical model\n"
                "3. Provide complete runnable code with UTF-8 encoding declaration\n"
                "4. Use English variable names and comments only\n"
                "5. Include proper error handling with try-except blocks\n"
                "6. Wrap code with ```python and ``` tags\n"
                "7. Ensure consistent code formatting and indentation"
            )},
            {"role": "user", "content": full_context if full_context else "No additional context"},
            {"role": "user", "content": f"Failed code:\n{failed_code}"},
            {"role": "user", "content": f"Error message:\n{error_message}"},
            {"role": "user", "content": (
                "Please fix syntax, API usage, encoding, and import errors in the code while keeping the mathematical model logic unchanged.\n"
                "MANDATORY FIXES:\n"
                "1. Add UTF-8 encoding declaration at the top\n"
                "2. Fix all import statements\n"
                "3. Use English variable names and comments\n"
                "4. Fix syntax and indentation errors\n"
                "5. Add proper error handling\n"
                f"Important reminder: {core_info}"
            )}
        ]
        
        repaired_code = query_llm(messages, self.model_name)
        self.repair_attempts += 1
        
        # Record repair attempt
        self.repair_history.append({
            "attempt": self.repair_attempts,
            "type": "code_level",
            "error": error_message[:200],
            "success": "pending_verification"
        })
        
        return repaired_code
    
    def _generate_model_defect_report(self, failed_code: str, error_message: str, 
                                     knowledge_matrix: dict = None, problem: str = "") -> ModelDefectReport:
        """Generate a model defect feedback report (Layer 2)."""
        print("[RepairAgent-Dual] Generating model defect feedback report...")
        
        core_info, full_context = self._create_focused_prompt(knowledge_matrix, problem)
        
        messages = [
            {"role": "system", "content": (
                "You are a mathematical modeling diagnostic expert. Analyze modeling defects behind code errors.\n"
                f"{core_info}\n"
                "Focus on identifying: missing constraints, variable type errors, objective function issues, model structure defects.\n"
                "Always respond in English only."
            )},
            {"role": "user", "content": full_context if full_context else "No additional context"},
            {"role": "user", "content": f"Failed code:\n{failed_code}"},
            {"role": "user", "content": f"Error message:\n{error_message}"},
            {"role": "user", "content": (
                "Please analyze mathematical model level defects and output in JSON format:\n"
                "{\"defect_type\": \"defect type\", \"description\": \"detailed description in English\", \"suggested_fixes\": [\"fix suggestion 1\", \"fix suggestion 2\"], \"severity\": \"severity level(low/medium/high/critical)\"}\n"
                "Use English only in all fields.\n"
                f"Important reminder: {core_info}"
            )}
        ]
        
        response = query_llm(messages, self.model_name)
        
        try:
            import json
            import re
            # Extract JSON from response
            json_match = re.search(r'\{[^{}]*\}', response)
            if json_match:
                defect_data = json.loads(json_match.group())
                report = ModelDefectReport(
                    defect_type=defect_data.get("defect_type", "unknown"),
                    description=defect_data.get("description", "Model has unknown defects"),
                    suggested_fixes=defect_data.get("suggested_fixes", ["Re-examine modeling approach"]),
                    severity=defect_data.get("severity", "medium")
                )
            else:
                report = ModelDefectReport(
                    defect_type="parsing_error",
                    description="Unable to parse model defect report",
                    suggested_fixes=["Regenerate mathematical model"],
                    severity="medium"
                )
        except Exception as e:
            print(f"[RepairAgent-Dual] Failed to parse defect report: {e}")
            report = ModelDefectReport(
                defect_type="analysis_error",
                description=f"Model analysis failed: {str(e)}",
                suggested_fixes=["Rebuild mathematical model"],
                severity="high"
            )
        
        return report
    
    def _should_escalate_to_model_level(self, error_type: str) -> bool:
        """Determine if repair should escalate to model level."""
        # Escalate if:
        # 1. Error is identified as MODEL_LEVEL or MIXED
        # 2. Multiple code-level repairs have failed
        # 3. Error patterns suggest fundamental model issues
        
        if error_type in ["MODEL_LEVEL", "MIXED"]:
            return True
            
        if self.repair_attempts >= self.max_code_repairs:
            print(f"[RepairAgent-Dual] Code-level repair reached maximum attempts ({self.max_code_repairs}), escalating to model level")
            return True
            
        return False
    
    def run(self, failed_code: str, error_message: str, knowledge_matrix: dict = None, 
           problem: str = "") -> Tuple[Optional[str], Optional[ModelDefectReport]]:
        """
        Execute dual-layer repair process.
        
        Args:
            failed_code (str): The code that failed to execute
            error_message (str): The error message from code execution
            knowledge_matrix (dict, optional): Knowledge matrix for context
            problem (str, optional): Original problem description
            
        Returns:
            Tuple[Optional[str], Optional[ModelDefectReport]]: 
                (repaired_code, model_defect_report)
                - If code-level repair succeeds: (repaired_code, None)
                - If model-level issues found: (None, defect_report)
        """
        print("[RepairAgent-Dual] Starting dual-layer repair process...")
        
        # Step 1: Analyze error type
        error_type = self._analyze_error_type(error_message, failed_code)
        
        # Step 2: Decide repair strategy
        if self._should_escalate_to_model_level(error_type):
            # Layer 2: Model-level feedback
            print("[RepairAgent-Dual] Escalating to model-level repair...")
            defect_report = self._generate_model_defect_report(
                failed_code, error_message, knowledge_matrix, problem
            )
            
            print(f"[RepairAgent-Dual] Model defect report generated - Type: {defect_report.defect_type}, Severity: {defect_report.severity}")
            return None, defect_report
        
        else:
            # Layer 1: Code-level repair
            repaired_code = self._perform_code_level_repair(
                failed_code, error_message, knowledge_matrix, problem
            )
            
            print("[RepairAgent-Dual] Code-level repair completed")
            return repaired_code, None
    
    def reset_repair_state(self):
        """Reset repair state for new problem."""
        self.repair_attempts = 0
        self.repair_history = []
    
    def get_repair_summary(self) -> dict:
        """Get summary of repair attempts."""
        return {
            "total_attempts": self.repair_attempts,
            "max_attempts": self.max_code_repairs,
            "history": self.repair_history
        }