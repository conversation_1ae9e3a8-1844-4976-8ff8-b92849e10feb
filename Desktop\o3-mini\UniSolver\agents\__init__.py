# -*- coding: utf-8 -*-
"""Agents Package - UniSolver 2.0

This package contains all the intelligent agents used in the upgraded UniSolver framework.
Each agent has been enhanced with advanced capabilities:

- InformationAgent: Hierarchical knowledge construction with long-context optimization
- ModelingAgent: Graph of Thoughts exploration for mathematical modeling
- CodeAgent: Enhanced code generation with focused prompting
- RepairAgent: Dual-layer repair system (code-level + model-level feedback)
- VerifierAgent: Unified answer generation, verification, correction and final formatting

Note: VerifierAgent is now the final gatekeeper responsible for all answer-related tasks.
"""

from .information_agent import InformationAgent
from .modeling_agent import ModelingAgent
from .code_agent import CodeAgent
from .repair_agent import RepairAgent, ModelDefectReport
from .verifier_agent import VerifierAgent

__all__ = [
    'InformationAgent',
    'ModelingAgent', 
    'CodeAgent',
    'RepairAgent',
    'ModelDefectReport',
    'VerifierAgent'
]