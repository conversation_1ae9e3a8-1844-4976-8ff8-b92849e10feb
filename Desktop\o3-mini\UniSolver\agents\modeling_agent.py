# -*- coding: utf-8 -*-
"""
Modeling Agent - Simplified Mathematical Modeling

Implements simplified approach for mathematical modeling with
direct model generation for optimization problems.
"""

import json
from typing import List, Dict, Tuple
from utils import query_llm


class ModelNode:
    """Represents a node in the modeling graph."""
    
    def __init__(self, model_id: str, model_content: str, approach: str, 
                 evaluation: Dict = None, parent_ids: List[str] = None):
        self.model_id = model_id
        self.model_content = model_content
        self.approach = approach
        self.evaluation = evaluation or {}
        self.parent_ids = parent_ids or []
        self.is_pruned = False


class ModelingAgent:
    """Agent responsible for simplified mathematical model generation."""
    
    def __init__(self, model_name="openai/o3-mini"):
        self.model_name = model_name
        self.model_graph = {}
        self.max_nodes = 6
        self.max_iterations = 3
    
    def _create_focused_prompt(self, knowledge_matrix: dict, problem: str) -> str:
        """Create a focused prompt with key information at start and end."""
        core_info = f"""Core Problem: {problem}
Key Knowledge: {knowledge_matrix.get('knowledge_graph', '')[:500]}..."""
        
        full_context = f"""## Search Results Summary:
{knowledge_matrix.get('search_context', '')}

## Complete Knowledge Graph:
{knowledge_matrix.get('knowledge_graph', '')}"""
        
        return core_info, full_context
    
    def _generate_diverse_nodes(self, problem: str, knowledge_matrix: dict) -> List[ModelNode]:
        """Generate multiple diverse modeling approaches (nodes)."""
        print("[ModelingAgent-GoT] Generating diverse modeling approaches...")
        
        core_info, full_context = self._create_focused_prompt(knowledge_matrix, problem)
        
        approaches = [
            ("linear_programming", "Linear programming approach focusing on simplicity and computational efficiency"),
            ("integer_programming", "Integer programming approach for precise discrete decisions"),
            ("mixed_integer", "Mixed integer programming balancing continuous and discrete variables"),
            ("constraint_focused", "Constraint-oriented approach emphasizing constraint completeness")
        ]
        
        nodes = []
        for i, (approach_type, approach_desc) in enumerate(approaches[:4]):
            messages = [
                {"role": "system", "content": (
                    f"You are an operations research expert specializing in {approach_desc}.\n"
                    f"Core Information: {core_info}\n"
                    "Requirement: Generate mathematical models unique to this method, highlighting its advantages."
                )},
                {"role": "user", "content": full_context},
                {"role": "user", "content": (
                    f"Model the following problem using {approach_desc}:\n{problem}\n\n"
                    "Generate a complete mathematical model including decision variables, objective function, and constraints.\n"
                    f"Important reminder: {core_info}"
                )}
            ]
            
            model_content = query_llm(messages, self.model_name)
            node = ModelNode(f"node_{i+1}", model_content, approach_type)
            nodes.append(node)
            
        return nodes
    
    def _evaluate_node(self, node: ModelNode, problem: str, knowledge_matrix: dict) -> Dict:
        """Perform heuristic evaluation of a modeling node."""
        print(f"[ModelingAgent-GoT] Evaluating node {node.model_id}...")
        
        core_info, _ = self._create_focused_prompt(knowledge_matrix, problem)
        
        messages = [
            {"role": "system", "content": (
                "You are an operations research model evaluation expert. Objectively evaluate mathematical models.\n"
                f"Core Information: {core_info}\n"
                "Evaluation dimensions: 1)Completeness 2)Solvability 3)Accuracy 4)Practicality"
            )},
            {"role": "user", "content": f"Model to evaluate:\n{node.model_content}"},
            {"role": "user", "content": (
                "Evaluate the above model and output in JSON format:\n"
                "{\"completeness\": score 1-10, \"solvability\": score 1-10, \"accuracy\": score 1-10, \"practicality\": score 1-10, \"strengths\": [list of strengths], \"weaknesses\": [list of weaknesses], \"overall_score\": total score 1-10}\n"
                f"Important reminder: {core_info}"
            )}
        ]
        
        evaluation_text = query_llm(messages, self.model_name)
        
        try:
            import re
            json_match = re.search(r'\{[^{}]*\}', evaluation_text)
            if json_match:
                evaluation = json.loads(json_match.group())
            else:
                evaluation = {"overall_score": 5, "error": "Failed to parse evaluation"}
        except:
            evaluation = {"overall_score": 5, "error": "Evaluation parsing failed"}
        
        node.evaluation = evaluation
        return evaluation
    
    def _aggregate_nodes(self, node1: ModelNode, node2: ModelNode, problem: str, knowledge_matrix: dict) -> ModelNode:
        """Aggregate two nodes to create a hybrid model."""
        print(f"[ModelingAgent-GoT] Aggregating nodes {node1.model_id} and {node2.model_id}...")
        
        core_info, _ = self._create_focused_prompt(knowledge_matrix, problem)
        
        messages = [
            {"role": "system", "content": (
                "You are an operations research model fusion expert. Merge the advantages of two models into a superior hybrid model.\n"
                f"Core Information: {core_info}"
            )},
            {"role": "user", "content": f"Model A ({node1.approach}):\n{node1.model_content}"},
            {"role": "user", "content": f"Model B ({node2.approach}):\n{node2.model_content}"},
            {"role": "user", "content": (
                "Merge the advantages of the above two models to generate a new hybrid model.\n"
                "Retain the best parts of both models and eliminate their respective defects.\n"
                f"Important reminder: {core_info}"
            )}
        ]
        
        aggregated_content = query_llm(messages, self.model_name)
        new_node_id = f"agg_{node1.model_id}_{node2.model_id}"
        
        return ModelNode(new_node_id, aggregated_content, "aggregated", 
                        parent_ids=[node1.model_id, node2.model_id])
    
    def _refine_node(self, node: ModelNode, problem: str, knowledge_matrix: dict) -> ModelNode:
        """Refine a node through iterative improvement."""
        print(f"[ModelingAgent-GoT] Refining node {node.model_id}...")
        
        core_info, _ = self._create_focused_prompt(knowledge_matrix, problem)
        
        messages = [
            {"role": "system", "content": (
                "You are an operations research model optimization expert. Deeply improve existing models to make them more complete.\n"
                f"Core Information: {core_info}"
            )},
            {"role": "user", "content": f"Current model:\n{node.model_content}"},
            {"role": "user", "content": (
                "Perform deep optimization on the above model:\n"
                "1. Add missing constraint conditions\n"
                "2. Optimize variable definitions\n"
                "3. Improve objective function expression\n"
                "4. Enhance mathematical rigor of the model\n"
                f"Important reminder: {core_info}"
            )}
        ]
        
        refined_content = query_llm(messages, self.model_name)
        new_node_id = f"ref_{node.model_id}"
        
        return ModelNode(new_node_id, refined_content, f"refined_{node.approach}", 
                        parent_ids=[node.model_id])
    
    def _intelligent_pruning(self, nodes: List[ModelNode]) -> List[ModelNode]:
        """Prune nodes with low potential based on evaluation scores."""
        print("[ModelingAgent-GoT] Performing intelligent pruning...")
        
        # Sort nodes by overall score
        evaluated_nodes = [node for node in nodes if node.evaluation.get('overall_score', 0) > 0]
        evaluated_nodes.sort(key=lambda x: x.evaluation.get('overall_score', 0), reverse=True)
        
        # Keep top 60% of nodes, prune the rest
        keep_count = max(2, int(len(evaluated_nodes) * 0.6))
        
        for i, node in enumerate(evaluated_nodes):
            if i >= keep_count:
                node.is_pruned = True
                print(f"[ModelingAgent-GoT] Pruning node {node.model_id} (score: {node.evaluation.get('overall_score', 0)})")
        
        return [node for node in evaluated_nodes if not node.is_pruned]
    
    def run(self, problem: str, knowledge_matrix: dict) -> str:
        """Execute simplified mathematical modeling."""
        print("[ModelingAgent] Starting mathematical modeling...")
        
        # Simplified approach: Generate one comprehensive model directly
        core_info, full_context = self._create_focused_prompt(knowledge_matrix, problem)
        
        messages = [
            {"role": "system", "content": (
                "You are a mathematical modeling expert specializing in operations research and optimization problems. "
                "Your task is to convert natural language problem descriptions into precise mathematical models.\n"
                f"Core Information: {core_info}\n"
                "REQUIREMENTS:\n"
                "1. Use English only in all outputs\n"
                "2. Provide clear and standardized mathematical notation\n"
                "3. Include complete model formulation with all components\n"
                "4. Use consistent variable naming conventions"
            )},
            {"role": "user", "content": full_context},
            {"role": "user", "content": (
                f"Model the following optimization problem:\n{problem}\n\n"
                "Based on the knowledge matrix above, create a comprehensive mathematical model for the optimization problem.\n"
                "REQUIRED COMPONENTS:\n"
                "1. Decision Variables: Define all variables with clear notation\n"
                "2. Objective Function: Specify maximize/minimize with complete expression\n"
                "3. Constraints: List all constraints with proper mathematical notation\n"
                "4. Variable Domains: Specify domains (continuous, integer, binary, non-negative)\n"
                "5. Model Summary: Brief explanation of the model structure\n"
                "Use English only and standard mathematical notation.\n"
                f"Important reminder: {core_info}"
            )}
        ]
        
        model_content = query_llm(messages, self.model_name)
        
        print("[ModelingAgent] Mathematical modeling completed")
        return model_content