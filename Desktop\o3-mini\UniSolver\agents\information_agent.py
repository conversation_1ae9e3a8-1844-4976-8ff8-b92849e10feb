# -*- coding: utf-8 -*-
"""
Information Agent - Hierarchical Knowledge Constructor

This agent implements recursive/hierarchical RAG (Retrieval Augmented Generation)
for building structured knowledge matrices. It decomposes complex problems into
sub-problems and constructs hierarchical knowledge trees.

Upgraded with long-context optimization and layered knowledge construction.
"""

from typing import Dict, List, Tuple
from utils import query_llm, google_custom_search, arxiv_search
import os


class KnowledgeNode:
    """Represents a node in the hierarchical knowledge tree."""
    
    def __init__(self, node_id: str, question: str, content: str = "", 
                 children: List['KnowledgeNode'] = None, level: int = 0):
        self.node_id = node_id
        self.question = question  # The sub-question this node addresses
        self.content = content    # The extracted knowledge content
        self.children = children or []
        self.level = level       # Depth in the hierarchy


class InformationAgent:
    """Hierarchical Information Retrieval Agent
    
    Uses hierarchical RAG method to build knowledge matrix:
    1. Problem decomposition: Break down complex problems into sub-problems
    2. Hierarchical search: Conduct targeted searches for each sub-problem
    3. Knowledge extraction: Extract relevant knowledge from search results
    4. Matrix construction: Build hierarchical knowledge matrix
    
    Optimization features:
    - Long-context optimization: Core information at front and back
    - Hierarchical structure: Support multi-level knowledge organization
    - Smart deduplication: Avoid duplicate processing of same problems
    - Focused search: Targeted keyword generation
    """
    
    def __init__(self, model_name="openai/o3-mini"):
        """
        Initialize the Information Agent with hierarchical capabilities.
        
        Args:
            model_name (str): LLM model name to use for processing
        """
        self.model_name = model_name
        self.max_depth = 2  # Reduced maximum recursion depth to avoid infinite loops
        self.knowledge_tree = None
        self.processed_questions = set()  # Track processed questions to avoid duplicates
    
    def _decompose_problem(self, problem: str) -> List[str]:
        """Decompose problem into sub-problems"""
        print(f"[InformationAgent-Hierarchical] Decomposing problem: {problem[:50]}...")
        
        messages = [
            {"role": "system", "content": (
                "You are an operations research problem analysis expert. Please break down complex optimization problems into key sub-problems.\n"
                "Each sub-problem should focus on one core aspect of the problem."
            )},
            {"role": "user", "content": (
                f"Please break down the following optimization problem into 5-7 key sub-problems:\n\n{problem}\n\n"
                "Sub-problems should include:\n"
                "1. What is the objective?\n"
                "2. What are the decision variables?\n"
                "3. What are the key constraints?\n"
                "4. What type of problem is this?\n"
                "5. What special domain knowledge is needed?\n"
                "6. Solutions to similar problems?\n"
                "7. Possible modeling approaches?\n\n"
                "Please output one sub-problem per line, format: Q1: Problem content"
            )}
        ]
        
        response = query_llm(messages, self.model_name)
        
        # Extract sub-questions
        sub_questions = []
        for line in response.split('\n'):
            line = line.strip()
            if line and (':' in line):
                # Extract question after the colon
                question = line.split(':', 1)[1].strip()
                if question:
                    sub_questions.append(question)
        
        return sub_questions[:7]  # Limit to 7 sub-questions
    
    def _generate_focused_keywords(self, sub_question: str, main_problem: str) -> List[str]:
        """Generate search keywords for sub-problems"""
        messages = [
            {"role": "system", "content": (
                "You are a search keyword expert. Generate precise search keywords for specific sub-problems."
            )},
            {"role": "user", "content": (
                f"Main problem: {main_problem}\n\n"
                f"Sub-problem: {sub_question}\n\n"
                "Please generate 3-4 precise search keywords for this sub-problem, separated by commas:"
            )}
        ]
        
        response = query_llm(messages, self.model_name)
        keywords = [kw.strip() for kw in response.replace('\n', ',').split(',') if kw.strip()]
        return keywords[:4]  # Limit to 4 keywords per sub-question
    
    def _extract_knowledge_for_subquestion(self, sub_question: str, search_results: List, 
                                          main_problem: str) -> str:
        """Extract knowledge for sub-problems from search results"""
        print(f"[InformationAgent-Hierarchical] Building knowledge node: {sub_question[:50]}...")
        
        # Create focused prompt with core information at start and end
        core_info = f"Core problem: {main_problem}\nSub-problem focus: {sub_question}"
        
        # Handle both string and dict results
        formatted_results = []
        for result in search_results[:5]:  # Limit to 5 most relevant results
            if isinstance(result, dict):
                formatted_results.append(f"Source: {result.get('title', 'Unknown')}\nContent: {result.get('snippet', result.get('summary', ''))}")
            else:
                formatted_results.append(str(result))
        
        search_context = "\n\n".join(formatted_results)
        
        messages = [
            {"role": "system", "content": (
                f"You are an operations research knowledge extraction expert.\n{core_info}\n"
                "Please extract knowledge directly related to the sub-problem from the search results."
            )},
            {"role": "user", "content": f"Search results:\n{search_context}"},
            {"role": "user", "content": (
                f"Please extract relevant knowledge from the above search results for sub-problem '{sub_question}':\n"
                "1. Directly answer the sub-problem\n"
                "2. Provide relevant technical details\n"
                "3. List relevant methods or concepts\n"
                "4. If there is no directly relevant information, please indicate\n\n"
                f"Important reminder: {core_info}"
            )}
        ]
        
        return query_llm(messages, self.model_name)
    
    def _build_knowledge_node(self, sub_question: str, main_problem: str, 
                             node_id: str, level: int = 0) -> KnowledgeNode:
        """Build a knowledge node for a specific sub-question."""
        print(f"[InformationAgent-Hierarchical] Extracting knowledge for sub-problem: {sub_question[:50]}...")
        
        # Check if this question has been processed before to avoid infinite loops
        question_key = sub_question.lower().strip()
        if question_key in self.processed_questions:
            print(f"[InformationAgent-Hierarchical] Skipping duplicate question: {sub_question[:30]}...")
            return KnowledgeNode(node_id, sub_question, "Processed duplicate question", level=level)
        
        self.processed_questions.add(question_key)
        
        # Generate focused keywords for this sub-question
        keywords = self._generate_focused_keywords(sub_question, main_problem)
        
        # Perform targeted searches
        search_results = []
        
        # Google search with focused keywords
        for keyword in keywords[:1]:  # Reduced to 1 keyword to speed up processing
            try:
                google_results = google_custom_search(keyword, max_results=1)
                search_results.extend(google_results)
            except Exception as e:
                print(f"[InformationAgent-Hierarchical] Google search failed ({keyword}): {e}")
        
        # Arxiv search for academic content
        if keywords and level == 1:  # Only search arxiv for top-level questions
            try:
                arxiv_results = arxiv_search(keywords[0], max_results=1)
                search_results.extend(arxiv_results)
            except Exception as e:
                print(f"[InformationAgent-Hierarchical] ArXiv search failed: {e}")
        
        # Extract knowledge for this sub-question
        knowledge_content = self._extract_knowledge_for_subquestion(
            sub_question, search_results, main_problem
        )
        
        # Create knowledge node
        node = KnowledgeNode(node_id, sub_question, knowledge_content, level=level)
        
        # Disable recursive decomposition to prevent infinite loops
        # Only allow one level of decomposition for the main problem
        if level == 0 and len(sub_question) > 100:  # Only decompose very complex main problems
            try:
                sub_sub_questions = self._decompose_problem(sub_question)[:2]  # Limit to 2 sub-sub-questions
                for i, sub_sub_q in enumerate(sub_sub_questions):
                    if sub_sub_q.lower().strip() not in self.processed_questions:  # Avoid duplicates
                        child_node = self._build_knowledge_node(
                            sub_sub_q, main_problem, f"{node_id}.{i+1}", level + 1
                        )
                        node.children.append(child_node)
            except Exception as e:
                print(f"[InformationAgent-Hierarchical] Recursive decomposition failed: {e}")
        
        return node
    
    def _construct_hierarchical_knowledge_matrix(self, root_node: KnowledgeNode, 
                                               main_problem: str) -> Dict:
        """Construct a hierarchical knowledge matrix from the knowledge tree."""
        print("[InformationAgent-Hierarchical] Building hierarchical knowledge matrix...")
        
        # Extract core information for long-context optimization
        core_knowledge = self._extract_core_knowledge(root_node, main_problem)
        
        # Build structured knowledge matrix
        knowledge_matrix = {
            "original_problem": main_problem,
            "core_knowledge": core_knowledge,  # Key information for long-context optimization
            "knowledge_tree": self._serialize_knowledge_tree(root_node),
            "hierarchical_summary": self._create_hierarchical_summary(root_node),
            "focused_context": self._create_focused_context(root_node, main_problem),
            "search_metadata": {
                "total_nodes": self._count_nodes(root_node),
                "max_depth": self._get_max_depth(root_node),
                "construction_method": "hierarchical_rag"
            }
        }
        
        return knowledge_matrix
    
    def _extract_core_knowledge(self, root_node: KnowledgeNode, main_problem: str) -> str:
        """Extract the most critical knowledge for long-context optimization."""
        # Collect all knowledge content
        all_content = []
        self._collect_all_content(root_node, all_content)
        
        combined_content = "\n\n".join(all_content)
        
        messages = [
            {"role": "system", "content": (
                "You are a knowledge refinement expert. Please extract the most critical knowledge points from extensive information.\n"
                f"Core problem: {main_problem}"
            )},
            {"role": "user", "content": f"All collected knowledge:\n{combined_content}"},
            {"role": "user", "content": (
                "Please extract the most critical knowledge points, including:\n"
                "1. Problem essence and objectives\n"
                "2. Key constraints\n"
                "3. Important domain knowledge\n"
                "4. Recommended modeling methods\n"
                "5. Special considerations\n\n"
                f"Important reminder: {main_problem}"
            )}
        ]
        
        return query_llm(messages, self.model_name)
    
    def _create_focused_context(self, root_node: KnowledgeNode, main_problem: str) -> str:
        """Create a focused context string optimized for long-context scenarios."""
        core_info = f"Core problem: {main_problem}"
        
        # Get hierarchical summary
        hierarchical_summary = self._create_hierarchical_summary(root_node)
        
        # Create focused context with core information at start and end
        focused_context = f"""{core_info}

## Hierarchical Knowledge Structure:
{hierarchical_summary}

## Detailed Knowledge Content:
{self._get_detailed_content(root_node)}

## Important Reminder:
{core_info}"""
        
        return focused_context
    
    def _serialize_knowledge_tree(self, node: KnowledgeNode, indent: int = 0) -> str:
        """Serialize the knowledge tree to a string representation."""
        result = "  " * indent + f"[{node.node_id}] {node.question}\n"
        result += "  " * (indent + 1) + f"Content: {node.content[:100]}...\n"
        
        for child in node.children:
            result += self._serialize_knowledge_tree(child, indent + 1)
        
        return result
    
    def _create_hierarchical_summary(self, root_node: KnowledgeNode) -> str:
        """Create a hierarchical summary of the knowledge tree."""
        summary_parts = []
        
        def traverse(node, level=0):
            prefix = "  " * level + "- "
            summary_parts.append(f"{prefix}{node.question}")
            for child in node.children:
                traverse(child, level + 1)
        
        traverse(root_node)
        return "\n".join(summary_parts)
    
    def _collect_all_content(self, node: KnowledgeNode, content_list: List[str]):
        """Recursively collect all content from the knowledge tree."""
        if node.content.strip():
            content_list.append(f"Q: {node.question}\nA: {node.content}")
        
        for child in node.children:
            self._collect_all_content(child, content_list)
    
    def _get_detailed_content(self, node: KnowledgeNode, level: int = 0) -> str:
        """Get detailed content from the knowledge tree."""
        result = ""
        if node.content.strip():
            indent = "  " * level
            result += f"{indent}Q: {node.question}\n{indent}A: {node.content}\n\n"
        
        for child in node.children:
            result += self._get_detailed_content(child, level + 1)
        
        return result
    
    def _count_nodes(self, node: KnowledgeNode) -> int:
        """Count total nodes in the knowledge tree."""
        count = 1
        for child in node.children:
            count += self._count_nodes(child)
        return count
    
    def _get_max_depth(self, node: KnowledgeNode) -> int:
        """Get maximum depth of the knowledge tree."""
        if not node.children:
            return node.level
        return max(self._get_max_depth(child) for child in node.children)
    
    def run(self, problem: str) -> dict:
        """
        Process the problem using hierarchical RAG and construct a knowledge matrix.
        
        Args:
            problem (str): The optimization problem description
            
        Returns:
            dict: Hierarchical knowledge matrix containing structured information
        """
        print("[InformationAgent-Hierarchical] Starting hierarchical knowledge construction...")
        
        # Reset processed questions for new problem
        self.processed_questions.clear()
        
        # Step 1: Decompose the main problem into sub-questions
        sub_questions = self._decompose_problem(problem)
        print(f"[InformationAgent-Hierarchical] Identified {len(sub_questions)} sub-questions")
        
        # Step 2: Build knowledge tree through simplified construction
        print("[InformationAgent-Hierarchical] Building knowledge tree...")
        
        # Create root node for the main problem
        root_node = KnowledgeNode("root", problem, level=0)
        
        # Build child nodes for each sub-question (limit to first 5 to avoid excessive processing)
        for i, sub_question in enumerate(sub_questions[:5]):
            print(f"[InformationAgent-Hierarchical] Processing sub-question {i+1}/{min(len(sub_questions), 5)}: {sub_question[:50]}...")
            child_node = self._build_knowledge_node(
                sub_question, problem, f"node_{i+1}", level=1
            )
            root_node.children.append(child_node)
        
        self.knowledge_tree = root_node
        
        # Step 3: Construct hierarchical knowledge matrix
        print("[InformationAgent-Hierarchical] Building knowledge matrix...")
        knowledge_matrix = self._construct_hierarchical_knowledge_matrix(root_node, problem)
        
        print(f"[InformationAgent-Hierarchical] Hierarchical knowledge construction completed (total nodes: {knowledge_matrix['search_metadata']['total_nodes']})")
        return knowledge_matrix